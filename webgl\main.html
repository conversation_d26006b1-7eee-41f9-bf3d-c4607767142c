<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  </head>
  <body>
    <div id="main-container" class="app-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <!-- 区域1：项目标识 -->
        <div class="header-left">
          <div class="logo">
            <img src="logo.png" alt="桂林智源" class="logo-image">
            <span class="logo-text">桂林智源</span>
          </div>
          <div class="system-title">SVG 数字化系统</div>
        </div>

        <!-- 区域2：功能栏 -->
        <div class="header-center">
          <div class="controls">
            <button class="control-btn primary" id="overview-btn">
              <i class="fas fa-eye"></i>
              总览视角
            </button>
            <button class="control-btn" id="tour-btn">
              <i class="fas fa-route"></i>
              自动漫游
            </button>
            <button class="control-btn" id="expand-btn">
              <i class="fas fa-expand-arrows-alt"></i>
              设备展开
            </button>
          </div>
        </div>

        <!-- 区域3：系统信息 -->
        <div class="header-right">
          <div class="system-info-panel">
            <div class="time-display" id="currentTime"></div>
            <div class="connection-status">
              <div class="status-indicator online">
                <i class="fas fa-circle"></i>
              </div>
              <span>连接状态正常</span>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 区域4：左栏 - 系统状态与参数 -->
        <aside class="left-panel">
          <div class="panel-header">
            <h3><i class="fas fa-cogs"></i>系统状态</h3>
          </div>

          <!-- 系统运行状态 -->
          <div class="status-section">
            <h4>系统状态</h4>
            <div class="status-grid">
              <div class="status-item" data-status="ready">
                <div class="status-indicator ready">
                  <i class="fas fa-circle"></i>
                </div>
                <span class="status-label">就绪</span>
              </div>

              <div class="status-item" data-status="fault">
                <div class="status-indicator fault">
                  <i class="fas fa-circle"></i>
                </div>
                <span class="status-label">故障</span>
              </div>

              <div class="status-item" data-status="charging">
                <div class="status-indicator charging">
                  <i class="fas fa-circle"></i>
                </div>
                <span class="status-label">充电</span>
              </div>

              <div class="status-item" data-status="waiting">
                <div class="status-indicator waiting">
                  <i class="fas fa-circle"></i>
                </div>
                <span class="status-label">等待</span>
              </div>

              <div class="status-item active" data-status="running">
                <div class="status-indicator running">
                  <i class="fas fa-circle"></i>
                </div>
                <span class="status-label">运行</span>
              </div>
            </div>

            <!-- 控制按钮区域 -->
            <div class="control-buttons">
              <button class="control-button start" onclick="controlSystem('start')">
                <i class="fas fa-play"></i>
                <span>启动</span>
              </button>

              <button class="control-button stop" onclick="controlSystem('stop')">
                <i class="fas fa-stop"></i>
                <span>停止</span>
              </button>

              <button class="control-button reset" onclick="controlSystem('reset')">
                <i class="fas fa-redo"></i>
                <span>复位</span>
              </button>
            </div>
          </div>

          <!-- 系统关键参数 -->
          <div class="parameters-section">
            <h4>关键参数</h4>
            <div class="parameter-list">
              <div class="parameter-item" onclick="showParameterDetails('current')">
                <div class="parameter-icon">
                  <i class="fas fa-bolt"></i>
                </div>
                <div class="parameter-content">
                  <div class="parameter-label">SVG 总电流</div>
                  <div class="parameter-value">125.8A</div>
                </div>
                <i class="fas fa-chevron-right"></i>
              </div>

              <div class="parameter-item" onclick="showParameterDetails('voltage')">
                <div class="parameter-icon">
                  <i class="fas fa-plug"></i>
                </div>
                <div class="parameter-content">
                  <div class="parameter-label">SVG 总电压</div>
                  <div class="parameter-value">10.5kV</div>
                </div>
                <i class="fas fa-chevron-right"></i>
              </div>

              <div class="parameter-item" onclick="showParameterDetails('power')">
                <div class="parameter-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="parameter-content">
                  <div class="parameter-label">功率因数</div>
                  <div class="parameter-value">0.95</div>
                </div>
                <i class="fas fa-chevron-right"></i>
              </div>
            </div>
          </div>

          <!-- 系统拓扑图 -->
          <div class="topology-section">
            <h4>系统拓扑图</h4>
            <div class="topology-container">
              <img src="svg-topology.png" alt="SVG系统拓扑图" class="topology-image">
              <div class="topology-placeholder" style="display: none;">
                <i class="fas fa-project-diagram"></i>
                <p>拓扑图加载中...</p>
              </div>
            </div>
          </div>
        </aside>

        <!-- 区域5：中栏 - 3D模型与故障信息 -->
        <section class="center-panel">
          <!-- 3D设备模型演示 -->
          <div class="unity-container" id="unityContainer">
            <!-- Unity WebGL 内容将在这里加载 -->
            <iframe id="unity-iframe" src="index.html" frameborder="0" class="unity-iframe"></iframe>
            <div class="unity-placeholder" id="unityPlaceholder" style="display: none;">
              <div class="placeholder-content">
                <i class="fas fa-cube rotating"></i>
                <h3>3D模型加载中...</h3>
                <p>正在初始化Unity WebGL引擎</p>
                <div class="loading-bar">
                  <div class="loading-progress" id="loadingProgress"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 3D场景控制工具栏 -->
          <div class="scene-toolbar">
            <div class="toolbar-group">
              <button class="toolbar-btn" title="重置视角">
                <i class="fas fa-home"></i>
              </button>
              <button class="toolbar-btn" title="全屏显示">
                <i class="fas fa-expand"></i>
              </button>
              <button class="toolbar-btn" title="截图">
                <i class="fas fa-camera"></i>
              </button>
            </div>
          </div>

          <!-- 故障信息展示 -->
          <div class="fault-info-section">
            <div class="fault-header">
              <h4><i class="fas fa-exclamation-triangle"></i>故障信息</h4>
              <div class="fault-filters">
                <button class="filter-btn active" data-filter="all">所有事件</button>
                <button class="filter-btn" data-filter="realtime">实时记录</button>
                <button class="filter-btn" data-filter="alarm">报警事件</button>
              </div>
            </div>
            <div class="fault-list" id="faultList">
              <!-- 故障信息将通过JavaScript动态生成 -->
            </div>
          </div>
        </section>

        <!-- 区域6：右栏 - 水冷系统 -->
        <aside class="right-panel">
          <div class="panel-header">
            <h3><i class="fas fa-tint"></i>水冷系统</h3>
          </div>

          <!-- 水冷系统状态 -->
          <div class="cooling-status-section">
            <div class="cooling-status-card">
              <div class="status-indicator running">
                <i class="fas fa-circle"></i>
              </div>
              <div class="status-content">
                <div class="status-label">运作状态</div>
                <div class="status-value">运行</div>
              </div>
            </div>
          </div>

          <!-- 水冷运行参数 -->
          <div class="cooling-parameters">
            <h4>运行参数</h4>
            <div class="parameter-grid">
              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">进水压力</div>
                  <div class="param-value">2.5MPa</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-water"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">进水流量</div>
                  <div class="param-value">15L/min</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">进水温度</div>
                  <div class="param-value">25°C</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">出水压力</div>
                  <div class="param-value">2.2MPa</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-thermometer-three-quarters"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">出水温度</div>
                  <div class="param-value">35°C</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 水冷拓扑图 -->
          <div class="cooling-topology-section">
            <h4>水冷拓扑图</h4>
            <div class="topology-container">
              <img src="cooling-topology.png" alt="水冷系统拓扑图" class="topology-image">
              <div class="topology-placeholder" style="display: none;">
                <i class="fas fa-project-diagram"></i>
                <p>拓扑图加载中...</p>
              </div>
            </div>
          </div>
        </aside>
      </main>

      <!-- 底部状态栏 -->
      <footer class="footer">
        <div class="footer-left">
          <div class="system-info">
            <span>系统版本: v2.1.0</span>
            <span>|</span>
            <span>最后更新: 2025-06-24 14:30:25</span>
          </div>
        </div>
        <div class="footer-center">
          <!-- 设备总数显示已移除 -->
        </div>
        <div class="footer-right">
          <div class="copyright">
            © 2025 桂林智源 - SVG 数字化系统
          </div>
        </div>
      </footer>
    </div>
    
    <script>
      /**
       * 桂林智源 SVG 数字化系统 - 主页面脚本
       * 处理Unity WebGL嵌入、参数监控和故障信息显示
       */

      var unityInstance = null;
      var unityIframe = null;

      // 页面加载完成后初始化
      window.addEventListener("load", function () {
        initMainPage();
        updateTime();
        setInterval(updateTime, 1000);
        // 初始化故障信息筛选
        initFaultFilters();
        // 模拟数据更新
        setInterval(updateSystemData, 5000);
        // 模拟状态变化（仅用于演示）
        setTimeout(() => {
          simulateStatusChange();
        }, 10000);
        // 初始化故障信息并开始模拟
        initializeFaultLog();
        startFaultSimulation();
      });

      /**
       * 更新时间显示
       */
      function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
          timeElement.textContent = timeString;
        }
      }

      /**
       * 初始化主页面
       */
      function initMainPage() {
        unityIframe = document.getElementById('unity-iframe');

        // 监听来自Unity iframe的消息
        window.addEventListener('message', function(event) {
          // 确保消息来源安全
          if (event.source !== unityIframe.contentWindow) {
            return;
          }

          if (event.data && event.data.type === 'unityLoaded') {
            console.log('Unity WebGL 加载完成');
            onUnityLoaded();
          }
        });

        // 绑定控制按钮事件
        bindControlEvents();

        // 绑定窗口大小变化事件
        window.addEventListener('resize', function() {
          resizeAllCharts();
        });

        // 添加按钮点击波纹效果
        addRippleEffect();
      }
      
      /**
       * Unity加载完成后的回调
       */
      function onUnityLoaded() {
        // 初始化图表
        initCharts();
      }
      
      /**
       * 绑定控制按钮事件
       */
      function bindControlEvents() {
        // 总览按钮
        document.getElementById("overview-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "SwitchToOverviewPosition");
          // 更新按钮状态
          updateButtonStates('overview-btn');
        });

        // 漫游按钮
        document.getElementById("tour-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "ToggleDeviceViewTour");
          // 更新按钮状态
          updateButtonStates('tour-btn');
        });

        // 展开/收起按钮
        document.getElementById("expand-btn").addEventListener("click", function() {
          sendUnityCommand("Device", "ToggleExpand");

          // 切换按钮文本和图标
          var expandBtn = document.getElementById("expand-btn");
          var icon = expandBtn.querySelector('i');
          var text = expandBtn.querySelector('span') || expandBtn.childNodes[expandBtn.childNodes.length - 1];

          if (text.textContent.trim() === "设备展开") {
            text.textContent = "设备收起";
            icon.className = "fas fa-compress-arrows-alt";
          } else {
            text.textContent = "设备展开";
            icon.className = "fas fa-expand-arrows-alt";
          }

          // 更新按钮状态
          updateButtonStates('expand-btn');
        });

        // 绑定图表控制按钮事件
        bindChartControlEvents();
      }

      /**
       * 更新按钮状态
       */
      function updateButtonStates(activeButtonId) {
        const buttons = document.querySelectorAll('.control-btn');
        buttons.forEach(btn => {
          if (btn.id === activeButtonId) {
            btn.classList.add('primary');
          } else {
            btn.classList.remove('primary');
          }
        });
      }

      /**
       * 绑定图表控制按钮事件
       */
      function bindChartControlEvents() {
        const chartBtns = document.querySelectorAll('.chart-btn');
        chartBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 移除其他按钮的active状态
            const parentControls = this.parentElement;
            parentControls.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');
          });
        });
      }
      
      /**
       * 向Unity发送命令
       * @param {string} target - 目标GameObject名称
       * @param {string} method - 要调用的方法名
       * @param {string} parameter - 可选参数
       */
      function sendUnityCommand(target, method, parameter) {
        try {
          if (unityIframe && unityIframe.contentWindow) {
            unityIframe.contentWindow.postMessage({
              type: 'unityCommand',
              target: target,
              method: method,
              parameter: parameter || ''
            }, '*');
            console.log('发送Unity命令:', target, method, parameter);
          } else {
            console.warn('Unity iframe 未准备就绪');
          }
        } catch (error) {
          console.error('发送Unity命令失败:', error);
        }
      }

      /**
       * 添加按钮点击波纹效果
       */
      function addRippleEffect() {
        const buttons = document.querySelectorAll('.control-btn, .toolbar-btn, .chart-btn');
        buttons.forEach(button => {
          button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
              ripple.remove();
            }, 600);
          });
        });
      }

      /**
       * 初始化故障信息筛选功能
       */
      function initFaultFilters() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 移除其他按钮的active状态
            filterBtns.forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');
            filterFaultList(filter);
          });
        });
      }

      /**
       * 筛选故障列表
       * @param {string} filter - 筛选类型：all, realtime, alarm
       */
      function filterFaultList(filter) {
        const faultItems = document.querySelectorAll('.fault-item');
        faultItems.forEach(item => {
          switch(filter) {
            case 'all':
              item.style.display = 'flex';
              break;
            case 'realtime':
              // 显示最近的记录
              item.style.display = 'flex';
              break;
            case 'alarm':
              // 只显示报警和故障
              if (item.classList.contains('warning') || item.classList.contains('error')) {
                item.style.display = 'flex';
              } else {
                item.style.display = 'none';
              }
              break;
          }
        });
      }

      /**
       * 显示参数详情页面
       * @param {string} paramType - 参数类型
       */
      function showParameterDetails(paramType) {
        console.log('显示参数详情:', paramType);
        // 这里可以实现跳转到详情页面或弹出详情窗口
        alert(`即将显示${paramType}参数的详细趋势曲线与历史数据`);
      }

      /**
       * 显示水冷系统详情
       */
      function showCoolingDetails() {
        console.log('显示水冷系统详情');
        alert('即将显示水冷系统详情页，查看实时数据曲线');
      }

      /**
       * 系统控制功能
       * @param {string} action - 控制动作：start, stop, reset
       */
      function controlSystem(action) {
        console.log('系统控制:', action);

        switch(action) {
          case 'start':
            // 模拟启动系统
            updateSystemStatus('running');
            alert('系统启动命令已发送');
            break;
          case 'stop':
            // 模拟停止系统
            updateSystemStatus('ready');
            alert('系统停止命令已发送');
            break;
          case 'reset':
            // 模拟复位系统
            updateSystemStatus('ready');
            alert('系统复位命令已发送');
            break;
        }
      }

      /**
       * 更新系统状态显示
       * @param {string} status - 状态：ready, fault, charging, waiting, running
       */
      function updateSystemStatus(status) {
        // 移除所有状态项的active类
        const statusItems = document.querySelectorAll('.status-item');
        statusItems.forEach(item => item.classList.remove('active'));

        // 为指定状态添加active类
        const targetItem = document.querySelector(`[data-status="${status}"]`);
        if (targetItem) {
          targetItem.classList.add('active');
        }
      }

      /**
       * 模拟状态变化（仅用于演示）
       */
      function simulateStatusChange() {
        const statuses = ['ready', 'charging', 'waiting', 'running'];
        let currentIndex = 3; // 从运行状态开始

        setInterval(() => {
          currentIndex = (currentIndex + 1) % statuses.length;
          updateSystemStatus(statuses[currentIndex]);

          // 添加状态变化日志到故障信息
          addFaultLog('normal', `系统状态切换为: ${getStatusName(statuses[currentIndex])}`);
        }, 15000); // 每15秒切换一次状态
      }

      /**
       * 获取状态中文名称
       */
      function getStatusName(status) {
        const statusNames = {
          'ready': '就绪',
          'fault': '故障',
          'charging': '充电',
          'waiting': '合高压等待',
          'running': '运行'
        };
        return statusNames[status] || status;
      }

      /**
       * 添加故障日志
       */
      function addFaultLog(type, message, timestamp = null) {
        const faultList = document.querySelector('.fault-list');
        if (faultList) {
          // 使用传入的时间戳或当前时间
          const now = timestamp ? new Date(timestamp) : new Date();

          // 手动格式化时间，避免Invalid Date问题
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          const seconds = String(now.getSeconds()).padStart(2, '0');

          const dateTimeStr = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

          const newItem = document.createElement('div');
          newItem.className = `fault-item ${type}`;
          newItem.dataset.timestamp = now.getTime(); // 存储时间戳用于排序
          newItem.innerHTML = `
            <span class="fault-index"></span>
            <span class="fault-datetime">${dateTimeStr}</span>
            <span class="fault-message">${message}</span>
          `;

          // 将新项目添加到数组中并按时间排序
          const allItems = Array.from(faultList.querySelectorAll('.fault-item'));
          allItems.push(newItem);

          // 按时间戳降序排序（最新的在前）
          allItems.sort((a, b) => {
            const timeA = parseInt(a.dataset.timestamp) || 0;
            const timeB = parseInt(b.dataset.timestamp) || 0;
            return timeB - timeA;
          });

          // 清空列表并重新添加排序后的项目
          faultList.innerHTML = '';
          allItems.forEach((item, index) => {
            // 更新序号
            const indexSpan = item.querySelector('.fault-index');
            if (indexSpan) {
              indexSpan.textContent = index + 1;
            }
            faultList.appendChild(item);
          });

          // 限制显示最多15条记录
          const finalItems = faultList.querySelectorAll('.fault-item');
          if (finalItems.length > 15) {
            for (let i = 15; i < finalItems.length; i++) {
              finalItems[i].remove();
            }
          }
        }
      }

      /**
       * 模拟系统数据更新
       */
      function updateSystemData() {
        // 更新SVG总电流
        const currentElement = document.querySelector('.parameter-item:nth-child(1) .parameter-value');
        if (currentElement) {
          const current = (125 + Math.random() * 10 - 5).toFixed(1);
          currentElement.textContent = current + 'A';
        }

        // 更新SVG总电压
        const voltageElement = document.querySelector('.parameter-item:nth-child(2) .parameter-value');
        if (voltageElement) {
          const voltage = (10.5 + Math.random() * 0.2 - 0.1).toFixed(1);
          voltageElement.textContent = voltage + 'kV';
        }

        // 更新功率因数
        const powerElement = document.querySelector('.parameter-item:nth-child(3) .parameter-value');
        if (powerElement) {
          const power = (0.95 + Math.random() * 0.04 - 0.02).toFixed(2);
          powerElement.textContent = power;
        }

        // 更新水冷系统参数
        updateCoolingParameters();
      }

      /**
       * 更新水冷系统参数
       */
      function updateCoolingParameters() {
        const coolingParams = document.querySelectorAll('.cooling-param-card .param-value');
        if (coolingParams.length >= 5) {
          // 进水压力
          const inPressure = (2.5 + Math.random() * 0.2 - 0.1).toFixed(1);
          coolingParams[0].textContent = inPressure + 'MPa';

          // 进水流量
          const inFlow = (15 + Math.random() * 2 - 1).toFixed(0);
          coolingParams[1].textContent = inFlow + 'L/min';

          // 进水温度
          const inTemp = (25 + Math.random() * 2 - 1).toFixed(0);
          coolingParams[2].textContent = inTemp + '°C';

          // 出水压力
          const outPressure = (2.2 + Math.random() * 0.2 - 0.1).toFixed(1);
          coolingParams[3].textContent = outPressure + 'MPa';

          // 出水温度
          const outTemp = (35 + Math.random() * 3 - 1.5).toFixed(0);
          coolingParams[4].textContent = outTemp + '°C';
        }
      }

      /**
       * 初始化故障日志
       */
      function initializeFaultLog() {
        // 添加系统启动日志
        addFaultLog('normal', '系统启动完成，所有设备运行正常');

        // 添加一些历史记录（模拟过去的事件）
        const now = new Date();
        const pastEvents = [
          { type: 'normal', message: 'SVG系统自检完成', offset: -180000 }, // 3分钟前
          { type: 'error', message: 'IGBT模块过热故障，已切换备用', offset: -360000 }, // 6分钟前
          { type: 'warning', message: '水冷系统温度略高，已自动调节', offset: -540000 }, // 9分钟前
          { type: 'error', message: '通信链路中断，正在重连', offset: -720000 }, // 12分钟前
          { type: 'normal', message: '电压参数调整完成', offset: -900000 }, // 15分钟前
          { type: 'warning', message: '功率因数偏低，建议检查负载', offset: -1080000 }, // 18分钟前
          { type: 'error', message: '电源模块异常，已启动保护', offset: -1260000 }, // 21分钟前
          { type: 'normal', message: '系统故障恢复，运行正常', offset: -1440000 }, // 24分钟前
          { type: 'warning', message: '直流母线电压波动', offset: -1620000 }, // 27分钟前
          { type: 'normal', message: '通信链路检测正常', offset: -1800000 }, // 30分钟前
        ];

        pastEvents.forEach(event => {
          const eventTime = new Date(now.getTime() + event.offset);
          addFaultLog(event.type, event.message, eventTime);
        });
      }

      /**
       * 开始故障模拟
       */
      function startFaultSimulation() {
        // 定期生成随机事件
        setInterval(() => {
          generateRandomFaultEvent();
        }, 15000 + Math.random() * 30000); // 15-45秒随机间隔，更频繁的事件生成
      }

      /**
       * 生成随机故障事件
       */
      function generateRandomFaultEvent() {
        const eventTypes = [
          { type: 'normal', weight: 50 },
          { type: 'warning', weight: 30 },
          { type: 'error', weight: 20 }
        ];

        const normalEvents = [
          '系统运行状态正常',
          'SVG模块参数调整完成',
          '水冷系统运行稳定',
          '电压电流参数正常',
          '功率因数调节完成',
          '通信状态良好',
          '设备温度正常',
          '系统自检通过',
          '负载平衡调整完成',
          '数据采集正常'
        ];

        const warningEvents = [
          '水冷系统温度偏高',
          '功率因数略低',
          '电压波动检测',
          '通信延迟增加',
          '负载不平衡警告',
          '温度传感器异常',
          '电流谐波超标',
          '系统响应时间延长',
          '设备运行时间过长',
          '环境温度升高',
          '直流母线电压偏低',
          'IGBT温度接近上限',
          '冷却液流量不足',
          '电抗器温度偏高',
          '变压器负载率高',
          '电容器容量下降',
          '风机转速异常',
          '绝缘电阻偏低',
          '谐波含量超标',
          '功率输出不稳定',
          '控制精度下降',
          '响应速度变慢',
          '数据采集延迟',
          '备用设备预警',
          '维护周期临近'
        ];

        const errorEvents = [
          'SVG模块通信中断',
          '水冷系统严重故障',
          '电压保护动作',
          '过流保护触发',
          '温度保护启动',
          '通信链路完全中断',
          '传感器读取失败',
          '控制器响应超时',
          '电源模块异常',
          '系统紧急停机',
          'IGBT模块过热故障',
          '直流母线电压异常',
          '交流接触器故障',
          '冷却泵电机故障',
          '变压器绝缘故障',
          '电抗器过载保护',
          '控制电源故障',
          '光纤通信中断',
          '主控板硬件故障',
          '风机系统故障',
          '绝缘监测报警',
          '接地故障检测',
          '谐波滤波器故障',
          '电容器组故障',
          '断路器拒动故障',
          '保护装置异常',
          '测量回路断线',
          '操作回路故障',
          '辅助电源故障',
          '人机界面通信故障'
        ];

        // 根据权重随机选择事件类型
        const totalWeight = eventTypes.reduce((sum, type) => sum + type.weight, 0);
        let random = Math.random() * totalWeight;
        let selectedType = 'normal';

        for (const eventType of eventTypes) {
          random -= eventType.weight;
          if (random <= 0) {
            selectedType = eventType.type;
            break;
          }
        }

        // 根据类型选择事件消息
        let events;
        switch (selectedType) {
          case 'warning':
            events = warningEvents;
            break;
          case 'error':
            events = errorEvents;
            break;
          default:
            events = normalEvents;
        }

        const randomMessage = events[Math.floor(Math.random() * events.length)];
        addFaultLog(selectedType, randomMessage);
      }
    </script>
  </body>
</html>
