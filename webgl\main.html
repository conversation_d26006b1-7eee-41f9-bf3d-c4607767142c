<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  </head>
  <body>
    <div id="main-container" class="app-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <!-- 区域1：项目标识 -->
        <div class="header-left">
          <div class="logo">
            <i class="fas fa-microchip"></i>
            <span class="logo-text">桂林智源</span>
          </div>
          <div class="system-title">SVG 数字化系统</div>
        </div>

        <!-- 区域2：功能栏 -->
        <div class="header-center">
          <div class="controls">
            <button class="control-btn primary" id="overview-btn">
              <i class="fas fa-eye"></i>
              总览视角
            </button>
            <button class="control-btn" id="tour-btn">
              <i class="fas fa-route"></i>
              自动漫游
            </button>
            <button class="control-btn" id="expand-btn">
              <i class="fas fa-expand-arrows-alt"></i>
              设备展开
            </button>
          </div>
        </div>

        <!-- 区域3：系统信息 -->
        <div class="header-right">
          <div class="system-info-panel">
            <div class="time-display" id="currentTime"></div>
            <div class="connection-status">
              <i class="fas fa-wifi"></i>
              <span>连接状态正常</span>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 区域4：左栏 - 系统状态与参数 -->
        <aside class="left-panel">
          <div class="panel-header">
            <h3><i class="fas fa-cogs"></i>系统状态</h3>
          </div>

          <!-- 系统运行状态 -->
          <div class="status-section">
            <div class="status-card">
              <div class="status-indicator running">
                <i class="fas fa-circle"></i>
              </div>
              <div class="status-content">
                <div class="status-label">运行状态</div>
                <div class="status-value">运行中</div>
              </div>
            </div>
          </div>

          <!-- 系统关键参数 -->
          <div class="parameters-section">
            <h4>关键参数</h4>
            <div class="parameter-list">
              <div class="parameter-item" onclick="showParameterDetails('current')">
                <div class="parameter-icon">
                  <i class="fas fa-bolt"></i>
                </div>
                <div class="parameter-content">
                  <div class="parameter-label">SVG 总电流</div>
                  <div class="parameter-value">125.8A</div>
                </div>
                <i class="fas fa-chevron-right"></i>
              </div>

              <div class="parameter-item" onclick="showParameterDetails('voltage')">
                <div class="parameter-icon">
                  <i class="fas fa-plug"></i>
                </div>
                <div class="parameter-content">
                  <div class="parameter-label">SVG 总电压</div>
                  <div class="parameter-value">10.5kV</div>
                </div>
                <i class="fas fa-chevron-right"></i>
              </div>

              <div class="parameter-item" onclick="showParameterDetails('power')">
                <div class="parameter-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="parameter-content">
                  <div class="parameter-label">功率因数</div>
                  <div class="parameter-value">0.95</div>
                </div>
                <i class="fas fa-chevron-right"></i>
              </div>
            </div>
          </div>

          <!-- 系统拓扑图 -->
          <div class="topology-section">
            <h4>系统拓扑图</h4>
            <div class="topology-container">
              <img src="topology-placeholder.svg" alt="SVG系统拓扑图" class="topology-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
              <div class="topology-placeholder" style="display: none;">
                <i class="fas fa-project-diagram"></i>
                <p>拓扑图加载中...</p>
              </div>
            </div>
          </div>
        </aside>

        <!-- 区域5：中栏 - 3D模型与故障信息 -->
        <section class="center-panel">
          <!-- 3D设备模型演示 -->
          <div class="unity-container" id="unityContainer">
            <!-- Unity WebGL 内容将在这里加载 -->
            <iframe id="unity-iframe" src="index.html" frameborder="0" class="unity-iframe"></iframe>
            <div class="unity-placeholder" id="unityPlaceholder" style="display: none;">
              <div class="placeholder-content">
                <i class="fas fa-cube rotating"></i>
                <h3>3D模型加载中...</h3>
                <p>正在初始化Unity WebGL引擎</p>
                <div class="loading-bar">
                  <div class="loading-progress" id="loadingProgress"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 3D场景控制工具栏 -->
          <div class="scene-toolbar">
            <div class="toolbar-group">
              <button class="toolbar-btn" title="重置视角">
                <i class="fas fa-home"></i>
              </button>
              <button class="toolbar-btn" title="全屏显示">
                <i class="fas fa-expand"></i>
              </button>
              <button class="toolbar-btn" title="截图">
                <i class="fas fa-camera"></i>
              </button>
            </div>
          </div>

          <!-- 故障信息展示 -->
          <div class="fault-info-section">
            <div class="fault-header">
              <h4><i class="fas fa-exclamation-triangle"></i>故障信息</h4>
              <div class="fault-filters">
                <button class="filter-btn active" data-filter="all">所有事件</button>
                <button class="filter-btn" data-filter="realtime">实时记录</button>
                <button class="filter-btn" data-filter="alarm">报警事件</button>
              </div>
            </div>
            <div class="fault-list">
              <div class="fault-item normal">
                <span class="fault-index">1</span>
                <span class="fault-date">02/14/25</span>
                <span class="fault-time">16:14:30</span>
                <span class="fault-message">系统正常运行</span>
              </div>
              <div class="fault-item warning">
                <span class="fault-index">2</span>
                <span class="fault-date">02/14/25</span>
                <span class="fault-time">15:45:12</span>
                <span class="fault-message">温度报警</span>
              </div>
              <div class="fault-item error">
                <span class="fault-index">3</span>
                <span class="fault-date">02/14/25</span>
                <span class="fault-time">14:32:08</span>
                <span class="fault-message">通信错误</span>
              </div>
            </div>
          </div>
        </section>

        <!-- 区域6：右栏 - 水冷系统 -->
        <aside class="right-panel">
          <div class="panel-header">
            <h3><i class="fas fa-tint"></i>水冷系统</h3>
          </div>

          <!-- 水冷系统状态 -->
          <div class="cooling-status-section">
            <div class="cooling-status-card">
              <div class="status-indicator running">
                <i class="fas fa-circle"></i>
              </div>
              <div class="status-content">
                <div class="status-label">运作状态</div>
                <div class="status-value">运行</div>
              </div>
            </div>
          </div>

          <!-- 水冷运行参数 -->
          <div class="cooling-parameters">
            <h4>运行参数</h4>
            <div class="parameter-grid">
              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">进水压力</div>
                  <div class="param-value">2.5MPa</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-water"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">进水流量</div>
                  <div class="param-value">15L/min</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">进水温度</div>
                  <div class="param-value">25°C</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">出水压力</div>
                  <div class="param-value">2.2MPa</div>
                </div>
              </div>

              <div class="cooling-param-card" onclick="showCoolingDetails()">
                <div class="param-icon">
                  <i class="fas fa-thermometer-three-quarters"></i>
                </div>
                <div class="param-content">
                  <div class="param-label">出水温度</div>
                  <div class="param-value">35°C</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 水冷拓扑图 -->
          <div class="cooling-topology-section">
            <h4>水冷拓扑图</h4>
            <div class="topology-container">
              <img src="topology-placeholder.svg" alt="水冷系统拓扑图" class="topology-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
              <div class="topology-placeholder" style="display: none;">
                <i class="fas fa-project-diagram"></i>
                <p>拓扑图加载中...</p>
              </div>
            </div>
          </div>
        </aside>
      </main>

      <!-- 底部状态栏 -->
      <footer class="footer">
        <div class="footer-left">
          <div class="system-info">
            <span>系统版本: v2.1.0</span>
            <span>|</span>
            <span>最后更新: 2025-06-24 14:30:25</span>
          </div>
        </div>
        <div class="footer-center">
          <div class="device-count">
            <i class="fas fa-microchip"></i>
            <span>设备总数: 12台</span>
          </div>
        </div>
        <div class="footer-right">
          <div class="copyright">
            © 2025 桂林智源 - SVG 数字化系统
          </div>
        </div>
      </footer>
    </div>
    
    <script>
      /**
       * 桂林智源 SVG 数字化系统 - 主页面脚本
       * 处理Unity WebGL嵌入、参数监控和故障信息显示
       */

      var unityInstance = null;
      var unityIframe = null;

      // 页面加载完成后初始化
      window.addEventListener("load", function () {
        initMainPage();
        updateTime();
        setInterval(updateTime, 1000);
        // 初始化故障信息筛选
        initFaultFilters();
        // 模拟数据更新
        setInterval(updateSystemData, 5000);
      });

      /**
       * 更新时间显示
       */
      function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
          timeElement.textContent = timeString;
        }
      }

      /**
       * 初始化主页面
       */
      function initMainPage() {
        unityIframe = document.getElementById('unity-iframe');

        // 监听来自Unity iframe的消息
        window.addEventListener('message', function(event) {
          // 确保消息来源安全
          if (event.source !== unityIframe.contentWindow) {
            return;
          }

          if (event.data && event.data.type === 'unityLoaded') {
            console.log('Unity WebGL 加载完成');
            onUnityLoaded();
          }
        });

        // 绑定控制按钮事件
        bindControlEvents();

        // 绑定窗口大小变化事件
        window.addEventListener('resize', function() {
          resizeAllCharts();
        });

        // 添加按钮点击波纹效果
        addRippleEffect();
      }
      
      /**
       * Unity加载完成后的回调
       */
      function onUnityLoaded() {
        // 初始化图表
        initCharts();
      }
      
      /**
       * 绑定控制按钮事件
       */
      function bindControlEvents() {
        // 总览按钮
        document.getElementById("overview-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "SwitchToOverviewPosition");
          // 更新按钮状态
          updateButtonStates('overview-btn');
        });

        // 漫游按钮
        document.getElementById("tour-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "ToggleDeviceViewTour");
          // 更新按钮状态
          updateButtonStates('tour-btn');
        });

        // 展开/收起按钮
        document.getElementById("expand-btn").addEventListener("click", function() {
          sendUnityCommand("Device", "ToggleExpand");

          // 切换按钮文本和图标
          var expandBtn = document.getElementById("expand-btn");
          var icon = expandBtn.querySelector('i');
          var text = expandBtn.querySelector('span') || expandBtn.childNodes[expandBtn.childNodes.length - 1];

          if (text.textContent.trim() === "设备展开") {
            text.textContent = "设备收起";
            icon.className = "fas fa-compress-arrows-alt";
          } else {
            text.textContent = "设备展开";
            icon.className = "fas fa-expand-arrows-alt";
          }

          // 更新按钮状态
          updateButtonStates('expand-btn');
        });

        // 绑定图表控制按钮事件
        bindChartControlEvents();
      }

      /**
       * 更新按钮状态
       */
      function updateButtonStates(activeButtonId) {
        const buttons = document.querySelectorAll('.control-btn');
        buttons.forEach(btn => {
          if (btn.id === activeButtonId) {
            btn.classList.add('primary');
          } else {
            btn.classList.remove('primary');
          }
        });
      }

      /**
       * 绑定图表控制按钮事件
       */
      function bindChartControlEvents() {
        const chartBtns = document.querySelectorAll('.chart-btn');
        chartBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 移除其他按钮的active状态
            const parentControls = this.parentElement;
            parentControls.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');
          });
        });
      }
      
      /**
       * 向Unity发送命令
       * @param {string} target - 目标GameObject名称
       * @param {string} method - 要调用的方法名
       * @param {string} parameter - 可选参数
       */
      function sendUnityCommand(target, method, parameter) {
        try {
          if (unityIframe && unityIframe.contentWindow) {
            unityIframe.contentWindow.postMessage({
              type: 'unityCommand',
              target: target,
              method: method,
              parameter: parameter || ''
            }, '*');
            console.log('发送Unity命令:', target, method, parameter);
          } else {
            console.warn('Unity iframe 未准备就绪');
          }
        } catch (error) {
          console.error('发送Unity命令失败:', error);
        }
      }

      /**
       * 添加按钮点击波纹效果
       */
      function addRippleEffect() {
        const buttons = document.querySelectorAll('.control-btn, .toolbar-btn, .chart-btn');
        buttons.forEach(button => {
          button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
              ripple.remove();
            }, 600);
          });
        });
      }

      /**
       * 初始化故障信息筛选功能
       */
      function initFaultFilters() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 移除其他按钮的active状态
            filterBtns.forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');
            filterFaultList(filter);
          });
        });
      }

      /**
       * 筛选故障列表
       * @param {string} filter - 筛选类型：all, realtime, alarm
       */
      function filterFaultList(filter) {
        const faultItems = document.querySelectorAll('.fault-item');
        faultItems.forEach(item => {
          switch(filter) {
            case 'all':
              item.style.display = 'flex';
              break;
            case 'realtime':
              // 显示最近的记录
              item.style.display = 'flex';
              break;
            case 'alarm':
              // 只显示报警和故障
              if (item.classList.contains('warning') || item.classList.contains('error')) {
                item.style.display = 'flex';
              } else {
                item.style.display = 'none';
              }
              break;
          }
        });
      }

      /**
       * 显示参数详情页面
       * @param {string} paramType - 参数类型
       */
      function showParameterDetails(paramType) {
        console.log('显示参数详情:', paramType);
        // 这里可以实现跳转到详情页面或弹出详情窗口
        alert(`即将显示${paramType}参数的详细趋势曲线与历史数据`);
      }

      /**
       * 显示水冷系统详情
       */
      function showCoolingDetails() {
        console.log('显示水冷系统详情');
        alert('即将显示水冷系统详情页，查看实时数据曲线');
      }

      /**
       * 模拟系统数据更新
       */
      function updateSystemData() {
        // 更新SVG总电流
        const currentElement = document.querySelector('.parameter-item:nth-child(1) .parameter-value');
        if (currentElement) {
          const current = (125 + Math.random() * 10 - 5).toFixed(1);
          currentElement.textContent = current + 'A';
        }

        // 更新SVG总电压
        const voltageElement = document.querySelector('.parameter-item:nth-child(2) .parameter-value');
        if (voltageElement) {
          const voltage = (10.5 + Math.random() * 0.2 - 0.1).toFixed(1);
          voltageElement.textContent = voltage + 'kV';
        }

        // 更新功率因数
        const powerElement = document.querySelector('.parameter-item:nth-child(3) .parameter-value');
        if (powerElement) {
          const power = (0.95 + Math.random() * 0.04 - 0.02).toFixed(2);
          powerElement.textContent = power;
        }

        // 更新水冷系统参数
        updateCoolingParameters();
      }

      /**
       * 更新水冷系统参数
       */
      function updateCoolingParameters() {
        const coolingParams = document.querySelectorAll('.cooling-param-card .param-value');
        if (coolingParams.length >= 5) {
          // 进水压力
          const inPressure = (2.5 + Math.random() * 0.2 - 0.1).toFixed(1);
          coolingParams[0].textContent = inPressure + 'MPa';

          // 进水流量
          const inFlow = (15 + Math.random() * 2 - 1).toFixed(0);
          coolingParams[1].textContent = inFlow + 'L/min';

          // 进水温度
          const inTemp = (25 + Math.random() * 2 - 1).toFixed(0);
          coolingParams[2].textContent = inTemp + '°C';

          // 出水压力
          const outPressure = (2.2 + Math.random() * 0.2 - 0.1).toFixed(1);
          coolingParams[3].textContent = outPressure + 'MPa';

          // 出水温度
          const outTemp = (35 + Math.random() * 3 - 1.5).toFixed(0);
          coolingParams[4].textContent = outTemp + '°C';
        }
      }
    </script>
  </body>
</html>
