<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>白云电气设备数字孪生系统</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  </head>
  <body>
    <div id="main-container" class="app-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <div class="header-left">
          <div class="logo">
            <i class="fas fa-bolt"></i>
            <span class="logo-text">白云电气</span>
          </div>
          <div class="system-title">设备数字孪生监控系统</div>
        </div>
        <div class="header-center">
          <div class="time-display" id="currentTime"></div>
        </div>
        <div class="header-right">
          <div class="status-indicators">
            <div class="status-item online">
              <i class="fas fa-circle"></i>
              <span>系统在线</span>
            </div>
            <div class="status-item">
              <i class="fas fa-users"></i>
              <span>3人在线</span>
            </div>
          </div>
          <div class="controls">
            <button class="control-btn primary" id="overview-btn">
              <i class="fas fa-eye"></i>
              总览视角
            </button>
            <button class="control-btn" id="tour-btn">
              <i class="fas fa-route"></i>
              自动漫游
            </button>
            <button class="control-btn" id="expand-btn">
              <i class="fas fa-expand-arrows-alt"></i>
              设备展开
            </button>
          </div>
        </div>
      </header>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 中央3D展示区域 -->
        <section class="center-panel">
          <div class="unity-container" id="unityContainer">
            <!-- Unity WebGL 内容将在这里加载 -->
            <iframe id="unity-iframe" src="index.html" frameborder="0" class="unity-iframe"></iframe>
            <div class="unity-placeholder" id="unityPlaceholder" style="display: none;">
              <div class="placeholder-content">
                <i class="fas fa-cube rotating"></i>
                <h3>3D模型加载中...</h3>
                <p>正在初始化Unity WebGL引擎</p>
                <div class="loading-bar">
                  <div class="loading-progress" id="loadingProgress"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 3D场景控制工具栏 -->
          <div class="scene-toolbar">
            <div class="toolbar-group">
              <button class="toolbar-btn" title="重置视角">
                <i class="fas fa-home"></i>
              </button>
              <button class="toolbar-btn" title="全屏显示">
                <i class="fas fa-expand"></i>
              </button>
              <button class="toolbar-btn" title="截图">
                <i class="fas fa-camera"></i>
              </button>
            </div>
          </div>
        </section>

        <!-- 右侧数据面板 -->
        <aside class="right-panel">
          <div class="panel-header">
            <h3>实时监控</h3>
          </div>

          <!-- 关键指标卡片 -->
          <div class="metrics-cards">
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-bolt"></i>
              </div>
              <div class="metric-content">
                <div class="metric-value">220.5V</div>
                <div class="metric-label">电压</div>
              </div>
              <div class="metric-trend up">
                <i class="fas fa-arrow-up"></i>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-thermometer-half"></i>
              </div>
              <div class="metric-content">
                <div class="metric-value">35.2°C</div>
                <div class="metric-label">温度</div>
              </div>
              <div class="metric-trend stable">
                <i class="fas fa-minus"></i>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-tachometer-alt"></i>
              </div>
              <div class="metric-content">
                <div class="metric-value">87%</div>
                <div class="metric-label">负载率</div>
              </div>
              <div class="metric-trend down">
                <i class="fas fa-arrow-down"></i>
              </div>
            </div>
          </div>

          <!-- 实时数据图表区域 -->
          <div class="charts-section">
            <div class="chart-container">
              <div class="chart-header">
                <h4>电压趋势</h4>
                <div class="chart-controls">
                  <button class="chart-btn active">1H</button>
                  <button class="chart-btn">6H</button>
                  <button class="chart-btn">24H</button>
                </div>
              </div>
              <div class="chart" id="chart1-content"></div>
            </div>

            <div class="chart-container">
              <div class="chart-header">
                <h4>温度监测</h4>
              </div>
              <div class="chart" id="chart2-content"></div>
            </div>

            <div class="chart-container">
              <div class="chart-header">
                <h4>系统负载</h4>
              </div>
              <div class="chart" id="chart3-content"></div>
            </div>
          </div>
        </aside>
      </main>

      <!-- 底部状态栏 -->
      <footer class="footer">
        <div class="footer-left">
          <div class="system-info">
            <span>系统版本: v2.1.0</span>
            <span>|</span>
            <span>最后更新: 2024-01-15 14:30:25</span>
          </div>
        </div>
        <div class="footer-center">
          <div class="connection-status">
            <i class="fas fa-wifi"></i>
            <span>连接状态: 正常</span>
            <div class="signal-strength">
              <div class="signal-bar active"></div>
              <div class="signal-bar active"></div>
              <div class="signal-bar active"></div>
              <div class="signal-bar"></div>
            </div>
          </div>
        </div>
        <div class="footer-right">
          <div class="copyright">
            © 2024 白云电气集团 - 数字孪生监控系统
          </div>
        </div>
      </footer>
    </div>
    
    <script>
      /**
       * 白云电气设备数字孪生系统 - 主页面脚本
       * 处理Unity WebGL嵌入和图表显示
       */

      var unityInstance = null;
      var unityIframe = null;

      // 页面加载完成后初始化
      window.addEventListener("load", function () {
        initMainPage();
        updateTime();
        setInterval(updateTime, 1000);
      });

      /**
       * 更新时间显示
       */
      function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
          timeElement.textContent = timeString;
        }
      }

      /**
       * 初始化主页面
       */
      function initMainPage() {
        unityIframe = document.getElementById('unity-iframe');

        // 监听来自Unity iframe的消息
        window.addEventListener('message', function(event) {
          // 确保消息来源安全
          if (event.source !== unityIframe.contentWindow) {
            return;
          }

          if (event.data && event.data.type === 'unityLoaded') {
            console.log('Unity WebGL 加载完成');
            onUnityLoaded();
          }
        });

        // 绑定控制按钮事件
        bindControlEvents();

        // 绑定窗口大小变化事件
        window.addEventListener('resize', function() {
          resizeAllCharts();
        });

        // 添加按钮点击波纹效果
        addRippleEffect();
      }
      
      /**
       * Unity加载完成后的回调
       */
      function onUnityLoaded() {
        // 初始化图表
        initCharts();
      }
      
      /**
       * 绑定控制按钮事件
       */
      function bindControlEvents() {
        // 总览按钮
        document.getElementById("overview-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "SwitchToOverviewPosition");
          // 更新按钮状态
          updateButtonStates('overview-btn');
        });

        // 漫游按钮
        document.getElementById("tour-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "ToggleDeviceViewTour");
          // 更新按钮状态
          updateButtonStates('tour-btn');
        });

        // 展开/收起按钮
        document.getElementById("expand-btn").addEventListener("click", function() {
          sendUnityCommand("Device", "ToggleExpand");

          // 切换按钮文本和图标
          var expandBtn = document.getElementById("expand-btn");
          var icon = expandBtn.querySelector('i');
          var text = expandBtn.querySelector('span') || expandBtn.childNodes[expandBtn.childNodes.length - 1];

          if (text.textContent.trim() === "设备展开") {
            text.textContent = "设备收起";
            icon.className = "fas fa-compress-arrows-alt";
          } else {
            text.textContent = "设备展开";
            icon.className = "fas fa-expand-arrows-alt";
          }

          // 更新按钮状态
          updateButtonStates('expand-btn');
        });

        // 绑定图表控制按钮事件
        bindChartControlEvents();
      }

      /**
       * 更新按钮状态
       */
      function updateButtonStates(activeButtonId) {
        const buttons = document.querySelectorAll('.control-btn');
        buttons.forEach(btn => {
          if (btn.id === activeButtonId) {
            btn.classList.add('primary');
          } else {
            btn.classList.remove('primary');
          }
        });
      }

      /**
       * 绑定图表控制按钮事件
       */
      function bindChartControlEvents() {
        const chartBtns = document.querySelectorAll('.chart-btn');
        chartBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 移除其他按钮的active状态
            const parentControls = this.parentElement;
            parentControls.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');
          });
        });
      }
      
      /**
       * 向Unity发送命令
       * @param {string} target - 目标GameObject名称
       * @param {string} method - 要调用的方法名
       * @param {string} parameter - 可选参数
       */
      function sendUnityCommand(target, method, parameter) {
        try {
          if (unityIframe && unityIframe.contentWindow) {
            unityIframe.contentWindow.postMessage({
              type: 'unityCommand',
              target: target,
              method: method,
              parameter: parameter || ''
            }, '*');
            console.log('发送Unity命令:', target, method, parameter);
          } else {
            console.warn('Unity iframe 未准备就绪');
          }
        } catch (error) {
          console.error('发送Unity命令失败:', error);
        }
      }

      /**
       * 添加按钮点击波纹效果
       */
      function addRippleEffect() {
        const buttons = document.querySelectorAll('.control-btn, .toolbar-btn, .chart-btn');
        buttons.forEach(button => {
          button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
              ripple.remove();
            }, 600);
          });
        });
      }

      /**
       * 模拟实时数据更新
       */
      function updateMetrics() {
        // 更新电压值
        const voltageElement = document.querySelector('.metric-card:nth-child(1) .metric-value');
        if (voltageElement) {
          const voltage = (220 + Math.random() * 2 - 1).toFixed(1);
          voltageElement.textContent = voltage + 'V';
        }

        // 更新温度值
        const tempElement = document.querySelector('.metric-card:nth-child(2) .metric-value');
        if (tempElement) {
          const temp = (35 + Math.random() * 4 - 2).toFixed(1);
          tempElement.textContent = temp + '°C';
        }

        // 更新负载率
        const loadElement = document.querySelector('.metric-card:nth-child(3) .metric-value');
        if (loadElement) {
          const load = Math.floor(85 + Math.random() * 10);
          loadElement.textContent = load + '%';
        }
      }

      // 每5秒更新一次指标数据
      setInterval(updateMetrics, 5000);
    </script>
  </body>
</html>
