<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>系统状态测试 - 桂林智源 SVG 数字化系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-microchip"></i>
                    <span class="logo-text">桂林智源</span>
                </div>
                <div class="system-title">SVG 数字化系统 - 状态测试</div>
            </div>
            <div class="header-center">
                <div style="color: var(--text-primary); font-size: 18px;">系统状态功能测试</div>
            </div>
            <div class="header-right">
                <div class="system-info-panel">
                    <div class="time-display" id="currentTime">2025/06/24 18:21:23</div>
                    <div class="connection-status">
                        <div class="status-indicator online">
                            <i class="fas fa-circle"></i>
                        </div>
                        <span>连接状态正常</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main style="flex: 1; display: flex; padding: 20px; gap: 20px;">
            <!-- 左侧状态面板 -->
            <div class="left-panel" style="width: 300px;">
                <div class="panel-header">
                    <h3><i class="fas fa-cogs"></i>系统状态</h3>
                </div>
                
                <!-- 系统运行状态 -->
                <div class="status-section">
                    <h4>系统状态</h4>
                    <div class="status-grid">
                        <div class="status-item" data-status="ready">
                            <div class="status-indicator ready">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">就绪</span>
                        </div>
                        
                        <div class="status-item" data-status="fault">
                            <div class="status-indicator fault">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">故障</span>
                        </div>
                        
                        <div class="status-item" data-status="charging">
                            <div class="status-indicator charging">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">充电</span>
                        </div>
                        
                        <div class="status-item" data-status="waiting">
                            <div class="status-indicator waiting">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">合高压等待</span>
                        </div>
                        
                        <div class="status-item active" data-status="running">
                            <div class="status-indicator running">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">运行</span>
                        </div>
                    </div>
                    
                    <!-- 控制按钮区域 -->
                    <div class="control-buttons">
                        <button class="control-button start" onclick="controlSystem('start')">
                            <i class="fas fa-play"></i>
                            <span>启动</span>
                        </button>
                        
                        <button class="control-button stop" onclick="controlSystem('stop')">
                            <i class="fas fa-stop"></i>
                            <span>停止</span>
                        </button>
                        
                        <button class="control-button reset" onclick="controlSystem('reset')">
                            <i class="fas fa-redo"></i>
                            <span>复位</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧说明区域 -->
            <div style="flex: 1; background: var(--bg-card); border: 1px solid var(--border-color); border-radius: 12px; padding: 30px;">
                <h2 style="color: var(--primary-color); margin-bottom: 20px;">系统状态功能说明</h2>
                
                <div style="color: var(--text-primary); line-height: 1.6;">
                    <h3 style="color: var(--accent-color); margin: 20px 0 10px 0;">状态指示器</h3>
                    <ul style="margin-left: 20px; color: var(--text-secondary);">
                        <li><strong style="color: var(--success-color);">就绪</strong> - 系统准备就绪，可以启动</li>
                        <li><strong style="color: var(--error-color);">故障</strong> - 系统出现故障，需要检修</li>
                        <li><strong style="color: var(--warning-color);">充电</strong> - 系统正在充电状态</li>
                        <li><strong style="color: var(--primary-color);">合高压等待</strong> - 等待高压合闸</li>
                        <li><strong style="color: var(--success-color);">运行</strong> - 系统正常运行中</li>
                    </ul>

                    <h3 style="color: var(--accent-color); margin: 20px 0 10px 0;">控制按钮</h3>
                    <ul style="margin-left: 20px; color: var(--text-secondary);">
                        <li><strong>启动</strong> - 启动系统运行</li>
                        <li><strong>停止</strong> - 停止系统运行</li>
                        <li><strong>复位</strong> - 重置系统状态</li>
                    </ul>

                    <h3 style="color: var(--accent-color); margin: 20px 0 10px 0;">交互说明</h3>
                    <ul style="margin-left: 20px; color: var(--text-secondary);">
                        <li>点击控制按钮可以切换系统状态</li>
                        <li>当前激活的状态会高亮显示</li>
                        <li>运行状态下指示器会有脉冲动画</li>
                        <li>页面会自动模拟状态变化（每15秒）</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 系统控制功能
        function controlSystem(action) {
            console.log('系统控制:', action);
            
            switch(action) {
                case 'start':
                    updateSystemStatus('running');
                    alert('系统启动命令已发送');
                    break;
                case 'stop':
                    updateSystemStatus('ready');
                    alert('系统停止命令已发送');
                    break;
                case 'reset':
                    updateSystemStatus('ready');
                    alert('系统复位命令已发送');
                    break;
            }
        }

        // 更新系统状态显示
        function updateSystemStatus(status) {
            const statusItems = document.querySelectorAll('.status-item');
            statusItems.forEach(item => item.classList.remove('active'));
            
            const targetItem = document.querySelector(`[data-status="${status}"]`);
            if (targetItem) {
                targetItem.classList.add('active');
            }
        }

        // 模拟状态变化
        function simulateStatusChange() {
            const statuses = ['ready', 'charging', 'waiting', 'running'];
            let currentIndex = 3;
            
            setInterval(() => {
                currentIndex = (currentIndex + 1) % statuses.length;
                updateSystemStatus(statuses[currentIndex]);
                console.log('状态切换为:', statuses[currentIndex]);
            }, 15000);
        }

        // 页面加载完成后初始化
        window.addEventListener("load", function () {
            updateTime();
            setInterval(updateTime, 1000);
            setTimeout(simulateStatusChange, 5000);
        });
    </script>
</body>
</html>
