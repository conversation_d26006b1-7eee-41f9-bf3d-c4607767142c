<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>1080p布局测试 - 桂林智源 SVG 数字化系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 测试用的视口信息显示 */
        .viewport-info {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
        }
        
        /* 确保body没有滚动条 */
        body {
            overflow: hidden;
        }
        
        .app-container {
            height: 100vh;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="viewport-info" id="viewportInfo">
        视口: <span id="viewportSize"></span> | 滚动: <span id="scrollInfo"></span>
    </div>

    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="logo.png" alt="桂林智源" class="logo-image">
                    <span class="logo-text">桂林智源</span>
                </div>
                <div class="system-title">SVG 数字化系统 - 1080p布局测试</div>
            </div>
            <div class="header-center">
                <div class="controls">
                    <button class="control-btn primary">
                        <i class="fas fa-eye"></i>
                        总览视角
                    </button>
                    <button class="control-btn">
                        <i class="fas fa-route"></i>
                        自动漫游
                    </button>
                    <button class="control-btn">
                        <i class="fas fa-expand-arrows-alt"></i>
                        设备展开
                    </button>
                </div>
            </div>
            <div class="header-right">
                <div class="system-info-panel">
                    <div class="time-display" id="currentTime">2025/06/24 18:21:23</div>
                    <div class="connection-status">
                        <div class="status-indicator online">
                            <i class="fas fa-circle"></i>
                        </div>
                        <span>连接状态正常</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-cogs"></i>系统状态</h3>
                </div>
                
                <!-- 系统运行状态 -->
                <div class="status-section">
                    <h4>系统状态</h4>
                    <div class="status-grid">
                        <div class="status-item" data-status="ready">
                            <div class="status-indicator ready">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">就绪</span>
                        </div>
                        <div class="status-item" data-status="fault">
                            <div class="status-indicator fault">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">故障</span>
                        </div>
                        <div class="status-item" data-status="charging">
                            <div class="status-indicator charging">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">充电</span>
                        </div>
                        <div class="status-item" data-status="waiting">
                            <div class="status-indicator waiting">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">等待</span>
                        </div>
                        <div class="status-item active" data-status="running">
                            <div class="status-indicator running">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span class="status-label">运行</span>
                        </div>
                    </div>
                    
                    <div class="control-buttons">
                        <button class="control-button start">
                            <i class="fas fa-play"></i>
                            <span>启动</span>
                        </button>
                        <button class="control-button stop">
                            <i class="fas fa-stop"></i>
                            <span>停止</span>
                        </button>
                        <button class="control-button reset">
                            <i class="fas fa-redo"></i>
                            <span>复位</span>
                        </button>
                    </div>
                </div>

                <!-- 关键参数 -->
                <div class="parameters-section">
                    <h4>关键参数</h4>
                    <div class="parameter-list">
                        <div class="parameter-item">
                            <div class="param-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="param-content">
                                <div class="param-label">SVG 总电流</div>
                                <div class="param-value">127.1A</div>
                            </div>
                        </div>
                        <div class="parameter-item">
                            <div class="param-icon">
                                <i class="fas fa-plug"></i>
                            </div>
                            <div class="param-content">
                                <div class="param-label">SVG 总电压</div>
                                <div class="param-value">10.4kV</div>
                            </div>
                        </div>
                        <div class="parameter-item">
                            <div class="param-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="param-content">
                                <div class="param-label">功率因数</div>
                                <div class="param-value">0.94</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统拓扑图 -->
                <div class="topology-section">
                    <h4>系统拓扑图</h4>
                    <div class="topology-container">
                        <div class="topology-placeholder">
                            <p>10kV母线</p>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                                <div style="width: 40px; height: 40px; border: 2px solid var(--primary-color); border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px;">QS</div>
                                <div style="width: 60px; height: 2px; background: var(--primary-color);"></div>
                                <div style="width: 40px; height: 40px; border: 2px solid var(--accent-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 10px;">SVG</div>
                            </div>
                            <p style="font-size: 12px; color: var(--accent-color);">SVG系统拓扑图</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间Unity WebGL区域 -->
            <div class="center-panel">
                <div class="unity-container">
                    <div class="unity-placeholder">
                        <div class="unity-loading">
                            <i class="fas fa-cube"></i>
                            <h3>Unity WebGL 3D场景</h3>
                            <p>1080p布局测试模式</p>
                            <div class="loading-bar">
                                <div class="loading-progress"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-snowflake"></i>水冷系统</h3>
                </div>
                
                <!-- 水冷系统状态 -->
                <div class="cooling-status-section">
                    <div class="cooling-status-card">
                        <div class="cooling-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div class="cooling-info">
                            <div class="cooling-title">水冷系统</div>
                            <div class="cooling-status running">运行正常</div>
                        </div>
                        <button class="details-btn" onclick="showCoolingDetails()">
                            <i class="fas fa-chart-line"></i>
                        </button>
                    </div>
                </div>

                <!-- 水冷参数 -->
                <div class="cooling-parameters">
                    <h4>水冷参数</h4>
                    <div class="parameter-grid">
                        <div class="cooling-param-card">
                            <div class="param-icon">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <div class="param-content">
                                <div class="param-label">进水温度</div>
                                <div class="param-value">25.3°C</div>
                            </div>
                        </div>
                        <div class="cooling-param-card">
                            <div class="param-icon">
                                <i class="fas fa-thermometer-full"></i>
                            </div>
                            <div class="param-content">
                                <div class="param-label">出水温度</div>
                                <div class="param-value">28.7°C</div>
                            </div>
                        </div>
                        <div class="cooling-param-card">
                            <div class="param-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="param-content">
                                <div class="param-label">流量</div>
                                <div class="param-value">45.2L/min</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 水冷拓扑图 -->
                <div class="cooling-topology-section">
                    <h4>水冷拓扑图</h4>
                    <div class="topology-container">
                        <div class="topology-placeholder">
                            <p style="font-size: 12px; margin-bottom: 10px;">水冷循环系统</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="width: 30px; height: 30px; border: 2px solid var(--success-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 10px;">泵</div>
                                <div style="width: 30px; height: 30px; border: 2px solid var(--primary-color); border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 8px;">冷却器</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部故障信息栏 -->
        <footer class="fault-info-section">
            <div class="fault-header">
                <h4><i class="fas fa-exclamation-triangle"></i>故障信息</h4>
                <div class="fault-filters">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="error">故障</button>
                    <button class="filter-btn" data-filter="warning">警告</button>
                    <button class="filter-btn" data-filter="normal">正常</button>
                </div>
            </div>
            <div class="fault-content">
                <div class="fault-list">
                    <div class="fault-item normal">
                        <span class="fault-index">1</span>
                        <span class="fault-date">06/24</span>
                        <span class="fault-time">18:21:23</span>
                        <span class="fault-message">系统启动完成，所有设备运行正常</span>
                    </div>
                    <div class="fault-item warning">
                        <span class="fault-index">2</span>
                        <span class="fault-date">06/24</span>
                        <span class="fault-time">18:20:15</span>
                        <span class="fault-message">水冷系统温度略高，建议检查散热</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 更新视口信息
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const scrollX = window.scrollX || window.pageXOffset;
            const scrollY = window.scrollY || window.pageYOffset;
            
            document.getElementById('viewportSize').textContent = `${width}x${height}`;
            document.getElementById('scrollInfo').textContent = `X:${scrollX} Y:${scrollY}`;
        }

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 页面加载完成后初始化
        window.addEventListener("load", function () {
            updateTime();
            setInterval(updateTime, 1000);
            updateViewportInfo();
            
            // 监听窗口大小变化和滚动
            window.addEventListener('resize', updateViewportInfo);
            window.addEventListener('scroll', updateViewportInfo);
        });

        // 水冷系统详情
        function showCoolingDetails() {
            alert('水冷系统详情页面');
        }
    </script>
</body>
</html>
